<?xml version="1.0" encoding="UTF-8"?>
<odoo>
	<record id="tree_access_model_view" model="ir.ui.view">
		<field name="name">tree_access.model</field>
		<field name="model">access.model</field>
		<field name="arch" type="xml">
			<tree string="Remove [Create/Edit/Delete]" editable="top">
				<field name="ir_model_id" required="1" />
				<field name="user_id" required="1"/>
                <field name="remove_create"/>
                <field name="remove_edit"/>
                <field name="remove_delete"/>
            </tree>
		</field>
	</record>
	<record id="action_access_model" model="ir.actions.act_window">
		<field name="name">Remove [Create/Edit/Delete]</field>
		<field name="type">ir.actions.act_window</field>
		<field name="res_model">access.model</field>
		<field name="view_mode">tree</field>
	</record>
	<menuitem id="menu_access_model" action="action_access_model" name="Access Models [Create/Edit/Delete]" parent="base.menu_users" groups="iwesabe_access_models.group_access_models"/>
</odoo>
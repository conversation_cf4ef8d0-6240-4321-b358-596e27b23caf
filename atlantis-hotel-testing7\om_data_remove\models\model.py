# -*- coding: utf-8 -*-

import logging
from odoo import api, fields, models, _

_logger = logging.getLogger(__name__)


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    company_to_clean = fields.Many2one('res.company', string='Company to Clean',
                                     default=lambda self: self.env.company,
                                     help="Select the company whose data you want to remove. "
                                          "Leave empty to remove data from all companies.")

    def remove_data(self, o, s=[]):
        company_id = self.company_to_clean.id if self.company_to_clean else False
        
        for line in o:
            try:
                if not self.env['ir.model']._get(line):
                    continue
            except Exception as e:
                _logger.warning('remove data error get ir.model: %s,%s', line, e)
                continue
            
            obj_name = line
            obj = self.pool.get(obj_name)
            if not obj:
                t_name = obj_name.replace('.', '_')
            else:
                t_name = obj._table

            # Check if model has company_id field
            has_company = False
            try:
                self.env['ir.model.fields'].search([
                    ('model', '=', obj_name),
                    ('name', '=', 'company_id')
                ])
                has_company = True
            except Exception as e:
                _logger.info('Model %s has no company_id field: %s', obj_name, e)

            if company_id and has_company:
                sql = "DELETE FROM %s WHERE company_id = %s" % (t_name, company_id)
            else:
                sql = "DELETE FROM %s" % t_name

            try:
                self._cr.execute(sql)
                self._cr.commit()
            except Exception as e:
                _logger.warning('remove data error: %s,%s', line, e)

        # Handle sequences
        for line in s:
            domain = ['|', ('code', '=ilike', line + '%'), ('prefix', '=ilike', line + '%')]
            if company_id:
                domain.append(('company_id', '=', company_id))
            
            try:
                seqs = self.env['ir.sequence'].sudo().search(domain)
                if seqs.exists():
                    seqs.write({
                        'number_next': 1,
                    })
            except Exception as e:
                _logger.warning('reset sequence data error: %s,%s', line, e)
        return True
    
    def remove_sales(self):
        to_removes = [
            'sale.order.line',
            'sale.order',
        ]
        seqs = [
            'sale',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_product(self):
        to_removes = [
            'product.product',
            'product.template',
        ]
        seqs = [
            'product.product',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_product_attribute(self):
        to_removes = [
            'product.attribute.value',
            'product.attribute',
        ]
        seqs = []
        return self.remove_data(to_removes, seqs)

    def remove_pos(self):
        company_id = self.company_to_clean.id if self.company_to_clean else False
        to_removes = [
            'pos.payment',
            'pos.order.line',
            'pos.order',
            'pos.session',
        ]
        seqs = [
            'pos.',
        ]
        res = self.remove_data(to_removes, seqs)
        try:
            domain = []
            if company_id:
                domain.append(('company_id', '=', company_id))
            statement = self.env['account.bank.statement'].sudo().search(domain)
            for s in statement:
                s._end_balance()
        except Exception as e:
            _logger.error('reset sequence data error: %s', e)
        return res

    def remove_purchase(self):
        to_removes = [
            'purchase.order.line',
            'purchase.order',
            'purchase.requisition.line',
            'purchase.requisition',
        ]
        seqs = [
            'purchase.',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_expense(self):
        to_removes = [
            'hr.expense.sheet',
            'hr.expense',
            'hr.payslip',
            'hr.payslip.run',
        ]
        seqs = [
            'hr.expense.',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_mrp(self):
        to_removes = [
            'mrp.workcenter.productivity',
            'mrp.workorder',
            'mrp.production.workcenter.line',
            'change.production.qty',
            'mrp.production',
            'mrp.production.product.line',
            'mrp.unbuild',
            'change.production.qty',
            'sale.forecast.indirect',
            'sale.forecast',
        ]
        seqs = [
            'mrp.',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_mrp_bom(self):
        to_removes = [
            'mrp.bom.line',
            'mrp.bom',
        ]
        seqs = []
        return self.remove_data(to_removes, seqs)

    def remove_stock_picking(self):
        to_removes = [
            'stock.move.line',
            'stock.package_level',
            'stock.move',
            'stock.picking',
            'stock.picking.batch',
        ]
        seqs = [
            'stock.picking',
            'picking.',
            'WH/IN/',
            'WH/OUT/',
            'WH/INT/',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_inventory(self):
        to_removes = [
            'stock.quant',
            'stock.quantity.history',
            'stock.quant.package',
            'stock.inventory.line',
            'stock.inventory',
            'stock.valuation.layer',
            'stock.production.lot',
            'procurement.group',
        ]
        seqs = [
            'stock.',
            'procurement.group',
            'product.tracking.default',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_account_moves(self):
        """Remove only journal entries and related move lines."""
        company_id = self.company_to_clean.id if self.company_to_clean else False
        # First, unreconcile all move lines
        try:
            if company_id:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE company_id=%s AND reconciled=True
                """, (company_id,))
            else:
                self._cr.execute("UPDATE account_move_line SET reconciled=False WHERE reconciled=True")
            self._cr.commit()
        except Exception as e:
            _logger.error('Error unreconciling move lines: %s', e)

        to_removes = [
            'account.partial.reconcile',  # Remove reconciliations first
            'account.move.line',          # Remove move lines
            'account.move',               # Remove moves (journal entries)
        ]
        res = self.remove_data(to_removes, [])
        
        # Reset journal entry sequences
        domain = [
            '|', ('code', '=ilike', 'account.%'),
            ('prefix', '=ilike', 'MISC/%'),
        ]
        
        if company_id:
            domain.insert(0, ('company_id', '=', company_id))
            
        try:
            seqs = self.env['ir.sequence'].search(domain)
            if seqs.exists():
                seqs.write({
                    'number_next': 1,
                })
        except Exception as e:
            _logger.error('reset sequence data error: %s,%s', domain, e)
        return res

    def remove_account_payments(self):
        """Remove payment-related records."""
        company_id = self.company_to_clean.id if self.company_to_clean else False
        to_removes = [
            'account.payment',            # Remove payments
            'payment.transaction',        # Remove payment transactions
        ]
        res = self.remove_data(to_removes, [])
        
        # Reset payment sequences
        domain = [
            '|', ('prefix', '=ilike', 'BNK1/%'),
            ('prefix', '=ilike', 'CSH1/%'),
        ]
        
        if company_id:
            domain.insert(0, ('company_id', '=', company_id))
            
        try:
            seqs = self.env['ir.sequence'].search(domain)
            if seqs.exists():
                seqs.write({
                    'number_next': 1,
                })
        except Exception as e:
            _logger.error('reset sequence data error: %s,%s', domain, e)
        return res

    def remove_account_bank_statements(self):
        """Remove bank statements and related records."""
        to_removes = [
            'account.bank.statement.line',
            'account.bank.statement',
        ]
        return self.remove_data(to_removes, [])

    def remove_account(self):
        """Remove all accounting data."""
        company_id = self.company_to_clean.id if self.company_to_clean else False
        # First, unreconcile all move lines
        try:
            if company_id:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE company_id=%s AND reconciled=True
                """, (company_id,))
            else:
                self._cr.execute("UPDATE account_move_line SET reconciled=False WHERE reconciled=True")
            self._cr.commit()
        except Exception as e:
            _logger.error('Error unreconciling move lines: %s', e)

        to_removes = [
            'account.partial.reconcile',  # Remove reconciliations first
            'account.analytic.line',      # Remove analytic entries
            'account.payment',            # Remove payments
            'payment.transaction',        # Remove payment transactions
            'account.bank.statement.line', # Remove bank statement lines
            'account.bank.statement',     # Remove bank statements
            'account.move.line',          # Remove move lines
            'account.move',               # Remove moves (journal entries)
            'hr.expense.sheet',           # Remove expense sheets
        ]
        res = self.remove_data(to_removes, [])
        
        # Reset all related sequences
        domain = [
            '|', ('code', '=ilike', 'account.%'),
            '|', ('prefix', '=ilike', 'BNK1/%'),
            '|', ('prefix', '=ilike', 'CSH1/%'),
            '|', ('prefix', '=ilike', 'INV/%'),
            '|', ('prefix', '=ilike', 'EXCH/%'),
            '|', ('prefix', '=ilike', 'MISC/%'),
            '|', ('prefix', '=ilike', '账单/%'),
            ('prefix', '=ilike', '杂项/%')
        ]
        
        if company_id:
            domain.insert(0, ('company_id', '=', company_id))
            
        try:
            seqs = self.env['ir.sequence'].search(domain)
            if seqs.exists():
                seqs.write({
                    'number_next': 1,
                })
        except Exception as e:
            _logger.error('reset sequence data error: %s,%s', domain, e)
        return res

    def remove_account_chart(self):
        company_id = self.company_to_clean.id if self.company_to_clean else self.env.company.id
        self = self.with_context(force_company=company_id, company_id=company_id)
        to_removes = [
            'res.partner.bank',
            'account.move.line',
            'account.invoice',
            'account.payment',
            'account.bank.statement',
            'account.tax.account.tag',
            'account.tax',
            'account.account.account.tag',
            'wizard_multi_charts_accounts',
            'account.journal',
            'account.account',
        ]
        try:
            field1 = self.env['ir.model.fields']._get('product.template', "taxes_id").id
            field2 = self.env['ir.model.fields']._get('product.template', "supplier_taxes_id").id

            sql = "delete from ir_default where (field_id = %s or field_id = %s) and company_id=%d" \
                  % (field1, field2, company_id)
            sql2 = "update account_journal set bank_account_id=NULL where company_id=%d;" % company_id
            self._cr.execute(sql)
            self._cr.execute(sql2)

            self._cr.commit()
        except Exception as e:
            _logger.error('remove data error: %s,%s', 'account_chart: set tax and account_journal', e)

        if self.env['ir.model']._get('pos.config'):
            domain = [('company_id', '=', company_id)] if company_id else []
            self.env['pos.config'].search(domain).write({
                'journal_id': False,
            })

        try:
            domain = [('company_id', '=', company_id)] if company_id else []
            rec = self.env['res.partner'].search(domain)
            for r in rec:
                r.write({
                    'property_account_receivable_id': None,
                    'property_account_payable_id': None,
                })
        except Exception as e:
            _logger.error('remove data error: %s,%s', 'account_chart', e)

        try:
            domain = [('company_id', '=', company_id)] if company_id else []
            rec = self.env['product.category'].search(domain)
            for r in rec:
                r.write({
                    'property_account_income_categ_id': None,
                    'property_account_expense_categ_id': None,
                    'property_account_creditor_price_difference_categ': None,
                    'property_stock_account_input_categ_id': None,
                    'property_stock_account_output_categ_id': None,
                    'property_stock_valuation_account_id': None,
                })
        except Exception as e:
            _logger.error('remove data error: %s,%s', 'account_chart', e)
        return self.remove_data(to_removes, [])

    def remove_project(self):
        to_removes = [
            'account.analytic.line',
            'project.task',
            'project.forecast',
            'project.project',
        ]
        seqs = []
        return self.remove_data(to_removes, seqs)

    def remove_quality(self):
        to_removes = [
            'quality.check',
            'quality.alert',
        ]
        seqs = [
            'quality.check',
            'quality.alert',
        ]
        return self.remove_data(to_removes, seqs)

    def remove_quality_setting(self):
        to_removes = [
            'quality.point',
            'quality.alert.stage',
            'quality.alert.team',
            'quality.point.test_type',
            'quality.reason',
            'quality.tag',
        ]
        return self.remove_data(to_removes)

    def remove_website(self):
        to_removes = [
            'blog.tag.category',
            'blog.tag',
            'blog.post',
            'blog.blog',
            'product.wishlist',
            'website.published.multi.mixin',
            'website.published.mixin',
            'website.multi.mixin',
            'website.visitor',
            'website.redirect',
            'website.seo.metadata',
        ]
        seqs = []
        return self.remove_data(to_removes, seqs)

    def remove_message(self):
        to_removes = [
            'mail.message',
            'mail.followers',
            'mail.activity',
        ]
        seqs = []
        return self.remove_data(to_removes, seqs)

    def remove_all(self):
        self.remove_account()
        self.remove_quality()
        self.remove_website()
        self.remove_quality_setting()
        self.remove_inventory()
        self.remove_purchase()
        self.remove_mrp()
        self.remove_sales()
        self.remove_project()
        self.remove_pos()
        self.remove_expense()
        self.remove_account_chart()
        self.remove_message()
        return True

    def reset_cat_loc_name(self):
        ids = self.env['product.category'].search([
            ('parent_id', '!=', False)
        ], order='complete_name')
        for rec in ids:
            try:
                rec._compute_complete_name()
            except:
                pass
        ids = self.env['stock.location'].search([
            ('location_id', '!=', False),
            ('usage', '!=', 'views'),
        ], order='complete_name')
        for rec in ids:
            try:
                rec._compute_complete_name()
            except:
                pass
        return True

    def remove_customer_invoices(self):
        """Remove customer invoices only."""
        company_id = self.company_to_clean.id if self.company_to_clean else False
        # First, unreconcile all move lines
        try:
            if company_id:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE company_id=%s AND reconciled=True
                    AND move_id IN (SELECT id FROM account_move WHERE move_type IN ('out_invoice', 'out_refund'))
                """, (company_id,))
            else:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE reconciled=True
                    AND move_id IN (SELECT id FROM account_move WHERE move_type IN ('out_invoice', 'out_refund'))
                """)
            self._cr.commit()
        except Exception as e:
            _logger.error('Error unreconciling invoice move lines: %s', e)

        # Remove invoice-related records
        try:
            if company_id:
                self._cr.execute("""
                    DELETE FROM account_partial_reconcile WHERE 
                    (credit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('out_invoice', 'out_refund') AND am.company_id = %s)
                    OR debit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('out_invoice', 'out_refund') AND am.company_id = %s))
                """, (company_id, company_id))
            else:
                self._cr.execute("""
                    DELETE FROM account_partial_reconcile WHERE 
                    (credit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('out_invoice', 'out_refund'))
                    OR debit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('out_invoice', 'out_refund')))
                """)
            self._cr.commit()
        except Exception as e:
            _logger.error('Error removing invoice reconciliations: %s', e)

        # Remove move lines and invoices
        to_removes = [
            'account.move.line',          # Remove move lines
            'account.move',               # Remove moves (invoices)
        ]
        
        # Add company filter for account.move
        if company_id:
            self.env['account.move'].search([
                ('company_id', '=', company_id),
                ('move_type', 'in', ['out_invoice', 'out_refund'])
            ]).unlink()
        else:
            self.env['account.move'].search([
                ('move_type', 'in', ['out_invoice', 'out_refund'])
            ]).unlink()
        
        # Reset invoice sequences
        domain = [
            ('prefix', '=ilike', 'INV/%'),
        ]
        if company_id:
            domain.append(('company_id', '=', company_id))
            
        try:
            seqs = self.env['ir.sequence'].search(domain)
            if seqs.exists():
                seqs.write({
                    'number_next': 1,
                })
        except Exception as e:
            _logger.error('reset sequence data error: %s,%s', domain, e)
        return True

    def remove_vendor_bills(self):
        """Remove vendor bills only."""
        company_id = self.company_to_clean.id if self.company_to_clean else False
        # First, unreconcile all move lines
        try:
            if company_id:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE company_id=%s AND reconciled=True
                    AND move_id IN (SELECT id FROM account_move WHERE move_type IN ('in_invoice', 'in_refund'))
                """, (company_id,))
            else:
                self._cr.execute("""
                    UPDATE account_move_line 
                    SET reconciled=False 
                    WHERE reconciled=True
                    AND move_id IN (SELECT id FROM account_move WHERE move_type IN ('in_invoice', 'in_refund'))
                """)
            self._cr.commit()
        except Exception as e:
            _logger.error('Error unreconciling bill move lines: %s', e)

        # Remove bill-related records
        try:
            if company_id:
                self._cr.execute("""
                    DELETE FROM account_partial_reconcile WHERE 
                    (credit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('in_invoice', 'in_refund') AND am.company_id = %s)
                    OR debit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('in_invoice', 'in_refund') AND am.company_id = %s))
                """, (company_id, company_id))
            else:
                self._cr.execute("""
                    DELETE FROM account_partial_reconcile WHERE 
                    (credit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('in_invoice', 'in_refund'))
                    OR debit_move_id IN (SELECT aml.id FROM account_move_line aml 
                        JOIN account_move am ON am.id = aml.move_id 
                        WHERE am.move_type IN ('in_invoice', 'in_refund')))
                """)
            self._cr.commit()
        except Exception as e:
            _logger.error('Error removing bill reconciliations: %s', e)

        # Remove move lines and bills
        if company_id:
            self.env['account.move'].search([
                ('company_id', '=', company_id),
                ('move_type', 'in', ['in_invoice', 'in_refund'])
            ]).unlink()
        else:
            self.env['account.move'].search([
                ('move_type', 'in', ['in_invoice', 'in_refund'])
            ]).unlink()
        
        # Reset bill sequences
        domain = [
            ('prefix', '=ilike', 'BILL/%'),
        ]
        if company_id:
            domain.append(('company_id', '=', company_id))
            
        try:
            seqs = self.env['ir.sequence'].search(domain)
            if seqs.exists():
                seqs.write({
                    'number_next': 1,
                })
        except Exception as e:
            _logger.error('reset sequence data error: %s,%s', domain, e)
        return True

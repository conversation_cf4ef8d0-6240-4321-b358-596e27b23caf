# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sh_journal_restrict
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-22 05:06+0000\n"
"PO-Revision-Date: 2021-07-22 05:06+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: sh_journal_restrict
#: model:ir.model.fields,field_description:sh_journal_restrict.field_account_journal__display_name
#: model:ir.model.fields,field_description:sh_journal_restrict.field_res_users__display_name
msgid "Display Name"
msgstr "Afficher un nom"

#. module: sh_journal_restrict
#: model:res.groups,name:sh_journal_restrict.group_journal_restrict_feature
msgid "Enable Journal Restrict Feature"
msgstr "Activer la fonctionnalité de journal restreindre"

#. module: sh_journal_restrict
#: model:ir.model.fields,field_description:sh_journal_restrict.field_account_journal__id
#: model:ir.model.fields,field_description:sh_journal_restrict.field_res_users__id
msgid "ID"
msgstr "identifiant"

#. module: sh_journal_restrict
#: model:ir.model,name:sh_journal_restrict.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: sh_journal_restrict
#: model:ir.model.fields,field_description:sh_journal_restrict.field_res_users__journal_ids
msgid "Journals"
msgstr "Journaux"

#. module: sh_journal_restrict
#: model:ir.model.fields,field_description:sh_journal_restrict.field_account_journal____last_update
#: model:ir.model.fields,field_description:sh_journal_restrict.field_res_users____last_update
msgid "Last Modified on"
msgstr "Dernière modification sur"

#. module: sh_journal_restrict
#: model:ir.model,name:sh_journal_restrict.model_res_users
#: model:ir.model.fields,field_description:sh_journal_restrict.field_account_journal__user_ids
msgid "Users"
msgstr "Utilisateurs"

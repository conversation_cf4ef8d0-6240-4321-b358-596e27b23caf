# -*- coding: utf-8 -*-
#############################################################################

#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2023-TODAY Cybrosys Technologies(<https://www.cybrosys.com>)
#    Author: Cybrosys Techno Solutions (<https://www.cybrosys.com>)
#
#    You can modify it under the terms of the GNU LESSER
#    GENERAL PUBLIC LICENSE (LGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU LESSER GENERAL PUBLIC LICENSE (LGPL v3) for more details.
#
#    You should have received a copy of the GNU LESSER GENERAL PUBLIC LICENSE
#    (LGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
#############################################################################
from odoo import fields, models


class SaleOrderLine(models.Model):
    """Sale order line inherited to add the domain to the order line product"""
    _inherit = 'sale.order.line'

    product_id = fields.Many2one('product.product',
                                 string='Product',
                                 domain="[('approve_state', '=', 'confirmed'),"
                                        " ('sale_ok', '=', True), '|', "
                                        "('company_id', '=', False),"
                                        "('company_id', '=', company_id)]",
                                 change_default=True, ondelete='restrict',
                                 check_company=True,
                                 help="product field in sale order line")

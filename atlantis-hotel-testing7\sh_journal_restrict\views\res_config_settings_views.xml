<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sh_res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.account</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div//div[@id = 'analytic']" position="after">
                <h2>Default Journal and Users</h2>
                <div class="row mt16 o_settings_container">
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_left_panel"></div>
                        <div class="o_setting_right_pane">
                            <div class="content-group">
                                <div class="row mt8">
                                    <label for="journal_ids" class="col-lg-5 o_light_label"/>
                                    <field name="journal_ids" widget = "many2many_tags"/>
                                </div>
                                <div class="row mt8">
                                    <label for="sh_user_ids" class="col-lg-5 o_light_label"/>
                                    <field name="sh_user_ids" widget = "many2many_tags"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
            
        </field>
    </record>
</odoo>